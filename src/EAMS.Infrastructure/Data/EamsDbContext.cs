using Microsoft.EntityFrameworkCore;
using EAMS.Domain.Entities;
using NetTopologySuite;
using NetTopologySuite.Geometries;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Microsoft.EntityFrameworkCore.ChangeTracking;

namespace EAMS.Infrastructure.Data;
public class EamsDbContext : DbContext
    {
        public DbSet<Accommodation> Accommodations { get; set; }
        public DbSet<Amenity> Amenities { get; set; }
        public DbSet<AmenityOptions> AmenityOptions { get; set; }
        public DbSet<User> Users { get; set; }
        public DbSet<UserInvitation> UserInvitations { get; set; }
        public DbSet<Organisation> Organisations { get; set; }

        public EamsDbContext(DbContextOptions<EamsDbContext> options) : base(options) {}


        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            // Configure many-to-many relationships
            modelBuilder.Entity<Organisation>()
                .HasMany(o => o.Accommodations)
                .WithMany(a => a.Organisations)
                .UsingEntity(j => j.ToTable("OrgAccommodation"));

            modelBuilder.Entity<Organisation>()
                .HasOne(o => o.ParentOrg)
                .WithMany(o => o.ChildOrganisations)
                .HasForeignKey(o => o.ParentOrgId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Accommodation>()
                .HasMany(a => a.AmenityOptions)
                .WithMany(ao => ao.Accommodations)
                .UsingEntity(j => j.ToTable("AccommodationAmenities"));

            // Configure one-to-many relationships with explicit foreign keys
            modelBuilder.Entity<User>()
                .HasOne(u => u.Organisation)
                .WithMany(o => o.Users)
                .HasForeignKey(u => u.OrganisationId);

            modelBuilder.Entity<UserInvitation>()
                .HasOne(ui => ui.InvitedUser)
                .WithMany(u => u.InvitationsReceived)
                .HasForeignKey(ui => ui.InvitedUserId)
                .OnDelete(DeleteBehavior.Restrict); // Prevent cascade delete

            modelBuilder.Entity<UserInvitation>()
                .HasOne(ui => ui.InvitedByUser)
                .WithMany(u => u.InvitationsSent)
                .HasForeignKey(ui => ui.InvitedByUserId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<UserInvitation>()
                .HasOne(ui => ui.TargetOrganisation)
                .WithMany(o => o.UserInvitations)
                .HasForeignKey(ui => ui.TargetOrganisationId);

            // Apply a global query filter for soft-deleted entities
            modelBuilder.Entity<Organisation>().HasQueryFilter(e => e.DiscardedAt == null);
            modelBuilder.Entity<User>().HasQueryFilter(e => e.DiscardedAt == null);
            modelBuilder.Entity<Accommodation>().HasQueryFilter(e => e.DiscardedAt == null);

            var gf = NtsGeometryServices.Instance.CreateGeometryFactory(4326);
            var converter = new ValueConverter<GeoPoint?, Point?>(
                to => to.HasValue
                        ? gf.CreatePoint(new Coordinate(to.Value.Longitude, to.Value.Latitude))
                        : null,
                from => from == null
                            ? (GeoPoint?)null
                            : GeoPoint.Create(from.Y, from.X),
                convertsNulls: true
            );
            var comparer = new ValueComparer<GeoPoint?>(
                (a,b) => a.HasValue == b.HasValue && (!a.HasValue || a.Value.Equals(b.Value)),
                v => v.HasValue ? HashCode.Combine(v.Value.Latitude, v.Value.Longitude) : 0,
                v => v
            );

            modelBuilder.Entity<Accommodation>(e =>
            {
                e.HasKey(x => x.Id);

                e.Property(x => x.Location)
                    .HasConversion(converter, comparer)
                    .HasColumnType("geography");
            });
        }
    }
