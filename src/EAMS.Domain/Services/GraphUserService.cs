﻿using EAMS.Domain.Entities;
using EAMS.Domain.Interfaces;
using Microsoft.Graph;
using Microsoft.Graph.Models;
using GraphUser = Microsoft.Graph.Models.User;
using User = EAMS.Domain.Entities.User;

namespace EAMS.Domain.Services;

public class GraphUserService : IGraphUserService
{
    private readonly GraphServiceClient _graphClient;
    private readonly IUserRepository _userRepository;

    public GraphUserService(GraphServiceClient graphClient, IUserRepository userRepository)
    {
        _graphClient = graphClient;  
        _userRepository = userRepository;
    }

    public async Task<User?> GetCurrentLoginUserAsync()
    {
        var graphUser = await _graphClient.Me.GetAsync(requestConfiguration =>
        {
            requestConfiguration.QueryParameters.Select = new string[] { "displayName", "givenName", "surname", "userPrincipalName", "id" };
        });

        if (graphUser is not null && !string.IsNullOrEmpty(graphUser.Id))
        {
            var userId = Guid.Parse(graphUser.Id);
            var user = await _userRepository.GetByIdAsync(userId);

        }
        
        return null;
    }

    public async Task<Invitation?> CreateInvitationAsync(UserInvitation newInvitation)
    {
        Invitation request = new Invitation()
        {
            InvitedUserEmailAddress = newInvitation.InvitedUserEmailAddress,
            InviteRedirectUrl = newInvitation.InviteRedirectUrl,
        };
        return await _graphClient.Invitations.PostAsync(request);
    }
}
